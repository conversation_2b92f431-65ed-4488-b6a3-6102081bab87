.data-visualization {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.viz-header {
  text-align: center;
  margin-bottom: 40px;
}

.viz-header h2 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 2.2em;
}

.viz-header p {
  color: #7f8c8d;
  font-size: 1.1em;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  border-left: 5px solid #3498db;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  font-size: 2.5em;
  margin-right: 20px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 2em;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.stat-title {
  color: #7f8c8d;
  font-size: 1em;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.chart-nav {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 30px;
  justify-content: center;
}

.chart-nav button {
  background: white;
  border: 2px solid #ddd;
  padding: 12px 20px;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
}

.chart-nav button:hover {
  border-color: #3498db;
  color: #3498db;
}

.chart-nav button.active {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

.charts-section {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 40px;
}

.chart-container h3 {
  color: #2c3e50;
  margin-bottom: 25px;
  font-size: 1.5em;
  text-align: center;
  border-bottom: 2px solid #ecf0f1;
  padding-bottom: 15px;
}

.no-data {
  text-align: center;
  color: #7f8c8d;
  font-size: 1.1em;
  padding: 40px;
  background: #f8f9fa;
  border-radius: 10px;
  border: 2px dashed #ddd;
}

.bar-chart {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.bar-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.bar-label {
  min-width: 150px;
  font-weight: 500;
  color: #2c3e50;
  text-align: right;
  font-size: 14px;
}

.bar-container {
  flex: 1;
  background: #f8f9fa;
  border-radius: 20px;
  height: 30px;
  position: relative;
  overflow: hidden;
}

.bar {
  height: 100%;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 10px;
  transition: width 0.8s ease;
  position: relative;
}

.bar-value {
  color: white;
  font-weight: bold;
  font-size: 12px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.recent-feedback {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.recent-feedback h3 {
  color: #2c3e50;
  margin-bottom: 25px;
  font-size: 1.5em;
  border-bottom: 2px solid #ecf0f1;
  padding-bottom: 15px;
}

.feedback-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.feedback-item {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid #3498db;
  transition: transform 0.3s ease;
}

.feedback-item:hover {
  transform: translateX(5px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.feedback-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  flex-wrap: wrap;
  gap: 10px;
}

.feedback-header strong {
  color: #2c3e50;
  font-size: 1.1em;
}

.feedback-location {
  background: #3498db;
  color: white;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

.feedback-rating {
  background: #f39c12;
  color: white;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

.feedback-reason {
  color: #2c3e50;
  font-style: italic;
  margin-bottom: 10px;
  line-height: 1.5;
}

.feedback-meta {
  color: #7f8c8d;
  font-size: 13px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
  .data-visualization {
    padding: 10px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .stat-card {
    padding: 20px;
  }
  
  .stat-icon {
    font-size: 2em;
    margin-right: 15px;
  }
  
  .stat-value {
    font-size: 1.5em;
  }
  
  .chart-nav {
    justify-content: center;
  }
  
  .chart-nav button {
    padding: 10px 15px;
    font-size: 12px;
  }
  
  .charts-section,
  .recent-feedback {
    padding: 20px;
  }
  
  .bar-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .bar-label {
    min-width: auto;
    text-align: left;
    font-size: 13px;
  }
  
  .feedback-header {
    flex-direction: column;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .viz-header h2 {
    font-size: 1.8em;
  }
  
  .stat-card {
    flex-direction: column;
    text-align: center;
  }
  
  .stat-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }
  
  .chart-nav button {
    padding: 8px 12px;
    font-size: 11px;
  }
}
