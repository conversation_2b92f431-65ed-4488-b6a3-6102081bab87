import { useState, useEffect } from 'react'
import './CampusMap.css'

const CampusMap = ({ surveyData }) => {
  const [selectedArea, setSelectedArea] = useState(null)
  const [hoveredArea, setHoveredArea] = useState(null)

  // Sample campus areas where students typically sit
  const campusAreas = [
    {
      id: 'library',
      name: 'Library',
      description: 'Quiet study spaces, reading rooms, and group study areas',
      x: 20,
      y: 30,
      width: 25,
      height: 20,
      color: '#4CAF50'
    },
    {
      id: 'cafeteria',
      name: 'Cafeteria',
      description: 'Food court, dining tables, and social eating areas',
      x: 55,
      y: 25,
      width: 30,
      height: 25,
      color: '#FF9800'
    },
    {
      id: 'garden',
      name: 'Campus Garden',
      description: 'Outdoor benches, lawn areas, and peaceful spots',
      x: 15,
      y: 60,
      width: 35,
      height: 25,
      color: '#8BC34A'
    },
    {
      id: 'auditorium',
      name: 'Auditorium Lobby',
      description: 'Spacious lobby with comfortable seating',
      x: 60,
      y: 55,
      width: 25,
      height: 20,
      color: '#9C27B0'
    },
    {
      id: 'courtyard',
      name: 'Central Courtyard',
      description: 'Open-air space with benches and tables',
      x: 35,
      y: 10,
      width: 20,
      height: 15,
      color: '#2196F3'
    },
    {
      id: 'computer-lab',
      name: 'Computer Lab',
      description: 'Tech-enabled study spaces and workstations',
      x: 70,
      y: 10,
      width: 20,
      height: 15,
      color: '#607D8B'
    }
  ]

  // Get survey data count for each area
  const getAreaPopularity = (areaId) => {
    return surveyData.filter(data => data.location === areaId).length
  }

  // Get area color based on popularity
  const getAreaColor = (area) => {
    const popularity = getAreaPopularity(area.id)
    if (popularity === 0) return area.color
    if (popularity <= 2) return '#FFE082'
    if (popularity <= 5) return '#FFCC02'
    return '#FF6F00'
  }

  return (
    <div className="campus-map">
      <div className="map-header">
        <h2>Interactive Campus Map</h2>
        <p>Click on different areas to see details and student feedback</p>
      </div>

      <div className="map-container">
        <svg viewBox="0 0 100 100" className="campus-svg">
          {/* Campus background */}
          <rect x="0" y="0" width="100" height="100" fill="#f0f8ff" stroke="#ddd" strokeWidth="0.5"/>
          
          {/* Campus areas */}
          {campusAreas.map(area => (
            <g key={area.id}>
              <rect
                x={area.x}
                y={area.y}
                width={area.width}
                height={area.height}
                fill={hoveredArea === area.id ? '#FFD54F' : getAreaColor(area)}
                stroke="#333"
                strokeWidth="0.5"
                className="area-rect"
                onMouseEnter={() => setHoveredArea(area.id)}
                onMouseLeave={() => setHoveredArea(null)}
                onClick={() => setSelectedArea(area)}
                style={{ cursor: 'pointer' }}
              />
              <text
                x={area.x + area.width / 2}
                y={area.y + area.height / 2}
                textAnchor="middle"
                dominantBaseline="middle"
                fontSize="2"
                fill="#333"
                className="area-label"
              >
                {area.name}
              </text>
              {/* Popularity indicator */}
              {getAreaPopularity(area.id) > 0 && (
                <circle
                  cx={area.x + area.width - 2}
                  cy={area.y + 2}
                  r="1.5"
                  fill="#FF5722"
                  className="popularity-indicator"
                />
              )}
            </g>
          ))}
        </svg>

        {/* Legend */}
        <div className="map-legend">
          <h3>Legend</h3>
          <div className="legend-item">
            <div className="legend-color" style={{ backgroundColor: '#f0f8ff' }}></div>
            <span>No data</span>
          </div>
          <div className="legend-item">
            <div className="legend-color" style={{ backgroundColor: '#FFE082' }}></div>
            <span>1-2 responses</span>
          </div>
          <div className="legend-item">
            <div className="legend-color" style={{ backgroundColor: '#FFCC02' }}></div>
            <span>3-5 responses</span>
          </div>
          <div className="legend-item">
            <div className="legend-color" style={{ backgroundColor: '#FF6F00' }}></div>
            <span>5+ responses</span>
          </div>
        </div>
      </div>

      {/* Area details panel */}
      {selectedArea && (
        <div className="area-details">
          <div className="details-header">
            <h3>{selectedArea.name}</h3>
            <button onClick={() => setSelectedArea(null)}>✕</button>
          </div>
          <p>{selectedArea.description}</p>
          <div className="area-stats">
            <div className="stat">
              <strong>Student Responses:</strong> {getAreaPopularity(selectedArea.id)}
            </div>
            {surveyData
              .filter(data => data.location === selectedArea.id)
              .slice(0, 3)
              .map((data, index) => (
                <div key={index} className="student-feedback">
                  <strong>Why they like it:</strong> {data.reason}
                  <br />
                  <small>Best time: {data.timeOfDay}</small>
                </div>
              ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default CampusMap
