# 🏫 College Campus Mapper

A comprehensive web application for mapping different parts of a college campus where students like to sit during their free time, complete with an interactive survey form to collect student preferences and feedback.

## 🌟 Features

### 📍 Interactive Campus Map
- Visual representation of different campus areas
- Clickable zones showing detailed information
- Real-time display of student feedback and popularity indicators
- Responsive SVG-based map design

### 📝 Comprehensive Survey Form
- **Personal Information**: Name, year of study, department
- **Location Preferences**: Favorite spots, best times to visit
- **Detailed Feedback**: Reasons for liking specific areas, ratings
- **User Experience**: Intuitive form validation and user-friendly interface

### 📊 Data Visualization & Analytics
- **Statistics Dashboard**: Total responses, locations covered, average ratings
- **Popular Spots**: Most frequented areas based on student feedback
- **Recent Feedback**: Latest student submissions with detailed insights
- **Real-time Updates**: Dynamic data visualization as new surveys are submitted

## 🎯 Campus Areas Covered

1. **📚 Library** - Quiet study spaces, reading rooms, and group study areas
2. **🍽️ Cafeteria** - Food court, dining tables, and social eating areas
3. **🌳 Campus Garden** - Outdoor benches, lawn areas, and peaceful spots
4. **🎭 Auditorium Lobby** - Spacious lobby with comfortable seating
5. **🏛️ Central Courtyard** - Open-air space with benches and tables
6. **💻 Computer Lab** - Tech-enabled study spaces and workstations

## 🚀 Technology Stack

- **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
- **Styling**: Modern CSS with gradients, animations, and responsive design
- **Data Storage**: Local Storage for persistent data
- **Graphics**: SVG for interactive campus map
- **Design**: Mobile-first responsive design

## 📱 Responsive Design

- **Desktop**: Full-featured experience with side-by-side layouts
- **Tablet**: Optimized grid layouts and touch-friendly interactions
- **Mobile**: Stacked layouts with easy navigation and form filling

## 🎨 Design Features

- **Modern UI**: Clean, professional interface with gradient backgrounds
- **Interactive Elements**: Hover effects, smooth transitions, and animations
- **Accessibility**: High contrast colors, readable fonts, and intuitive navigation
- **Visual Feedback**: Real-time form validation and success messages

## 📊 Data Collection

The survey form collects comprehensive information including:
- Student demographics (year, department)
- Location preferences and usage patterns
- Time-based preferences (best times to visit)
- Qualitative feedback (reasons for liking specific spots)
- Quantitative ratings (1-10 scale)
- Suggestions for improvements

## 🔧 Setup & Usage

1. **Simple Setup**: No installation required - just open the HTML file
2. **Browser Compatibility**: Works on all modern browsers
3. **Data Persistence**: Survey responses are saved locally
4. **Instant Feedback**: Real-time updates to map and statistics

## 📈 Use Cases

- **Student Services**: Understanding student preferences for campus improvements
- **Facility Planning**: Data-driven decisions for campus development
- **Student Orientation**: Helping new students discover popular hangout spots
- **Academic Research**: Studying student behavior and space utilization
- **Campus Events**: Planning events in popular student areas

## 🎯 Future Enhancements

- **Backend Integration**: Database storage for multi-user data sharing
- **Advanced Analytics**: Charts, graphs, and trend analysis
- **Photo Uploads**: Visual documentation of favorite spots
- **Social Features**: Comments, reviews, and student discussions
- **Mobile App**: Native mobile application for better accessibility
- **Real-time Updates**: Live data synchronization across users

## 🤝 Contributing

This project serves as a foundation for campus mapping initiatives. Feel free to:
- Customize campus areas for your specific institution
- Add new survey questions or data fields
- Enhance the visual design and user experience
- Integrate with existing campus systems

## 📄 License

Open source project - feel free to use, modify, and distribute for educational purposes.

---

**Built with ❤️ for students, by students**

*Making campus life more connected and informed through data-driven insights.*
