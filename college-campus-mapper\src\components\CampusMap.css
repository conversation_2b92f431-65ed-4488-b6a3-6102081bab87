.campus-map {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.map-header {
  text-align: center;
  margin-bottom: 30px;
}

.map-header h2 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.map-header p {
  color: #7f8c8d;
  font-size: 16px;
}

.map-container {
  display: flex;
  gap: 30px;
  align-items: flex-start;
}

.campus-svg {
  width: 100%;
  max-width: 800px;
  height: auto;
  border: 2px solid #ddd;
  border-radius: 10px;
  background: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.area-rect {
  transition: all 0.3s ease;
  opacity: 0.8;
}

.area-rect:hover {
  opacity: 1;
  stroke-width: 1;
}

.area-label {
  pointer-events: none;
  font-weight: bold;
}

.popularity-indicator {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.map-legend {
  min-width: 200px;
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.map-legend h3 {
  margin-top: 0;
  color: #2c3e50;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.legend-color {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  margin-right: 10px;
  border: 1px solid #ddd;
}

.area-details {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  z-index: 1000;
}

.area-details::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: -1;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.details-header h3 {
  margin: 0;
  color: #2c3e50;
}

.details-header button {
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.details-header button:hover {
  background: #c0392b;
}

.area-stats {
  margin-top: 20px;
}

.stat {
  background: #f8f9fa;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 15px;
  border-left: 4px solid #3498db;
}

.student-feedback {
  background: #e8f5e8;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 10px;
  border-left: 4px solid #27ae60;
}

.student-feedback strong {
  color: #27ae60;
}

.student-feedback small {
  color: #7f8c8d;
  font-style: italic;
}

/* Responsive design */
@media (max-width: 768px) {
  .map-container {
    flex-direction: column;
  }
  
  .map-legend {
    min-width: auto;
    width: 100%;
  }
  
  .area-details {
    width: 95%;
    padding: 20px;
  }
  
  .campus-map {
    padding: 10px;
  }
}
