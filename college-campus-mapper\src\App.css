/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: #333;
}

#root {
  min-height: 100vh;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 30px 20px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border-bottom: 3px solid #3498db;
}

.app-header h1 {
  font-size: 2.5em;
  color: #2c3e50;
  margin-bottom: 10px;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.app-header p {
  font-size: 1.2em;
  color: #7f8c8d;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.app-nav {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 20px;
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.app-nav button {
  background: white;
  border: 2px solid #ddd;
  padding: 15px 25px;
  border-radius: 30px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.app-nav button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  border-color: #3498db;
  color: #3498db;
}

.app-nav button.active {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border-color: #3498db;
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.3);
}

.app-main {
  flex: 1;
  padding: 40px 20px;
  background: rgba(255, 255, 255, 0.05);
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-header {
    padding: 20px 15px;
  }

  .app-header h1 {
    font-size: 2em;
  }

  .app-header p {
    font-size: 1em;
  }

  .app-nav {
    padding: 15px;
    gap: 10px;
  }

  .app-nav button {
    padding: 12px 20px;
    font-size: 14px;
  }

  .app-main {
    padding: 20px 10px;
  }
}

@media (max-width: 480px) {
  .app-header h1 {
    font-size: 1.8em;
  }

  .app-nav button {
    padding: 10px 15px;
    font-size: 12px;
    flex: 1;
    min-width: 120px;
  }
}
