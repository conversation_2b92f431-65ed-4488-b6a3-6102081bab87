import { useState } from 'react'
import './SurveyForm.css'

const SurveyForm = ({ onSubmit }) => {
  const [formData, setFormData] = useState({
    name: '',
    year: '',
    department: '',
    location: '',
    timeOfDay: '',
    frequency: '',
    reason: '',
    activities: [],
    groupSize: '',
    suggestions: '',
    rating: 5
  })

  const [errors, setErrors] = useState({})

  const campusLocations = [
    { id: 'library', name: 'Library' },
    { id: 'cafeteria', name: 'Cafeteria' },
    { id: 'garden', name: 'Campus Garden' },
    { id: 'auditorium', name: 'Auditorium Lobby' },
    { id: 'courtyard', name: 'Central Courtyard' },
    { id: 'computer-lab', name: 'Computer Lab' }
  ]

  const timeOptions = [
    'Early Morning (6-9 AM)',
    'Morning (9-12 PM)',
    'Afternoon (12-3 PM)',
    'Evening (3-6 PM)',
    'Late Evening (6-9 PM)'
  ]

  const frequencyOptions = [
    'Daily',
    'Few times a week',
    'Once a week',
    'Occasionally',
    'Rarely'
  ]

  const activityOptions = [
    'Studying',
    'Reading',
    'Socializing',
    'Eating',
    'Relaxing',
    'Group discussions',
    'Phone calls',
    'Listening to music',
    'Playing games',
    'Working on projects'
  ]

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const handleActivityChange = (activity) => {
    setFormData(prev => ({
      ...prev,
      activities: prev.activities.includes(activity)
        ? prev.activities.filter(a => a !== activity)
        : [...prev.activities, activity]
    }))
  }

  const validateForm = () => {
    const newErrors = {}
    
    if (!formData.name.trim()) newErrors.name = 'Name is required'
    if (!formData.year) newErrors.year = 'Year is required'
    if (!formData.department.trim()) newErrors.department = 'Department is required'
    if (!formData.location) newErrors.location = 'Please select a location'
    if (!formData.timeOfDay) newErrors.timeOfDay = 'Please select a time'
    if (!formData.frequency) newErrors.frequency = 'Please select frequency'
    if (!formData.reason.trim()) newErrors.reason = 'Please tell us why you like this spot'
    if (formData.activities.length === 0) newErrors.activities = 'Please select at least one activity'
    if (!formData.groupSize) newErrors.groupSize = 'Please select group size'

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    if (validateForm()) {
      onSubmit(formData)
      // Reset form
      setFormData({
        name: '',
        year: '',
        department: '',
        location: '',
        timeOfDay: '',
        frequency: '',
        reason: '',
        activities: [],
        groupSize: '',
        suggestions: '',
        rating: 5
      })
      alert('Thank you for sharing your favorite spot! 🎉')
    }
  }

  return (
    <div className="survey-form">
      <div className="form-header">
        <h2>Share Your Favorite Campus Spot</h2>
        <p>Help other students discover great places to hang out during free time!</p>
      </div>

      <form onSubmit={handleSubmit} className="survey-form-container">
        {/* Personal Information */}
        <div className="form-section">
          <h3>About You</h3>
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="name">Name (Optional)</label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Your name or nickname"
                className={errors.name ? 'error' : ''}
              />
              {errors.name && <span className="error-message">{errors.name}</span>}
            </div>
            <div className="form-group">
              <label htmlFor="year">Year of Study</label>
              <select
                id="year"
                name="year"
                value={formData.year}
                onChange={handleInputChange}
                className={errors.year ? 'error' : ''}
              >
                <option value="">Select Year</option>
                <option value="1st Year">1st Year</option>
                <option value="2nd Year">2nd Year</option>
                <option value="3rd Year">3rd Year</option>
                <option value="4th Year">4th Year</option>
                <option value="Graduate">Graduate</option>
              </select>
              {errors.year && <span className="error-message">{errors.year}</span>}
            </div>
          </div>
          <div className="form-group">
            <label htmlFor="department">Department/Course</label>
            <input
              type="text"
              id="department"
              name="department"
              value={formData.department}
              onChange={handleInputChange}
              placeholder="e.g., Computer Science, BCA, MBA"
              className={errors.department ? 'error' : ''}
            />
            {errors.department && <span className="error-message">{errors.department}</span>}
          </div>
        </div>

        {/* Location Information */}
        <div className="form-section">
          <h3>Your Favorite Spot</h3>
          <div className="form-group">
            <label htmlFor="location">Location</label>
            <select
              id="location"
              name="location"
              value={formData.location}
              onChange={handleInputChange}
              className={errors.location ? 'error' : ''}
            >
              <option value="">Select a location</option>
              {campusLocations.map(loc => (
                <option key={loc.id} value={loc.id}>{loc.name}</option>
              ))}
            </select>
            {errors.location && <span className="error-message">{errors.location}</span>}
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="timeOfDay">Best Time to Visit</label>
              <select
                id="timeOfDay"
                name="timeOfDay"
                value={formData.timeOfDay}
                onChange={handleInputChange}
                className={errors.timeOfDay ? 'error' : ''}
              >
                <option value="">Select time</option>
                {timeOptions.map(time => (
                  <option key={time} value={time}>{time}</option>
                ))}
              </select>
              {errors.timeOfDay && <span className="error-message">{errors.timeOfDay}</span>}
            </div>
            <div className="form-group">
              <label htmlFor="frequency">How Often Do You Visit?</label>
              <select
                id="frequency"
                name="frequency"
                value={formData.frequency}
                onChange={handleInputChange}
                className={errors.frequency ? 'error' : ''}
              >
                <option value="">Select frequency</option>
                {frequencyOptions.map(freq => (
                  <option key={freq} value={freq}>{freq}</option>
                ))}
              </select>
              {errors.frequency && <span className="error-message">{errors.frequency}</span>}
            </div>
          </div>
        </div>

        {/* Activities and Preferences */}
        <div className="form-section">
          <h3>What You Do There</h3>
          <div className="form-group">
            <label>Activities (Select all that apply)</label>
            <div className="checkbox-grid">
              {activityOptions.map(activity => (
                <label key={activity} className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={formData.activities.includes(activity)}
                    onChange={() => handleActivityChange(activity)}
                  />
                  <span>{activity}</span>
                </label>
              ))}
            </div>
            {errors.activities && <span className="error-message">{errors.activities}</span>}
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="groupSize">Usually go there</label>
              <select
                id="groupSize"
                name="groupSize"
                value={formData.groupSize}
                onChange={handleInputChange}
                className={errors.groupSize ? 'error' : ''}
              >
                <option value="">Select option</option>
                <option value="Alone">Alone</option>
                <option value="With 1-2 friends">With 1-2 friends</option>
                <option value="Small group (3-5)">Small group (3-5)</option>
                <option value="Large group (6+)">Large group (6+)</option>
              </select>
              {errors.groupSize && <span className="error-message">{errors.groupSize}</span>}
            </div>
            <div className="form-group">
              <label htmlFor="rating">Rate this spot (1-10)</label>
              <input
                type="range"
                id="rating"
                name="rating"
                min="1"
                max="10"
                value={formData.rating}
                onChange={handleInputChange}
                className="rating-slider"
              />
              <div className="rating-display">{formData.rating}/10</div>
            </div>
          </div>
        </div>

        {/* Additional Information */}
        <div className="form-section">
          <h3>Tell Us More</h3>
          <div className="form-group">
            <label htmlFor="reason">Why do you like this spot?</label>
            <textarea
              id="reason"
              name="reason"
              value={formData.reason}
              onChange={handleInputChange}
              placeholder="What makes this place special? Is it quiet, has good WiFi, comfortable seating, etc."
              rows="4"
              className={errors.reason ? 'error' : ''}
            />
            {errors.reason && <span className="error-message">{errors.reason}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="suggestions">Suggestions for Improvement (Optional)</label>
            <textarea
              id="suggestions"
              name="suggestions"
              value={formData.suggestions}
              onChange={handleInputChange}
              placeholder="Any suggestions to make this spot even better?"
              rows="3"
            />
          </div>
        </div>

        <button type="submit" className="submit-button">
          Share My Spot 🚀
        </button>
      </form>
    </div>
  )
}

export default SurveyForm
