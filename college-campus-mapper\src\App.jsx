import { useState } from 'react'
import './App.css'
import CampusMap from './components/CampusMap'
import SurveyForm from './components/SurveyForm'
import DataVisualization from './components/DataVisualization'

function App() {
  const [activeTab, setActiveTab] = useState('map')
  const [surveyData, setSurveyData] = useState([])

  const handleSurveySubmit = (data) => {
    setSurveyData(prev => [...prev, { ...data, id: Date.now() }])
    setActiveTab('map')
  }

  return (
    <div className="app">
      <header className="app-header">
        <h1>🏫 College Campus Mapper</h1>
        <p>Discover and share the best spots where students hang out during free time</p>
      </header>

      <nav className="app-nav">
        <button
          className={activeTab === 'map' ? 'active' : ''}
          onClick={() => setActiveTab('map')}
        >
          📍 Campus Map
        </button>
        <button
          className={activeTab === 'survey' ? 'active' : ''}
          onClick={() => setActiveTab('survey')}
        >
          📝 Share Your Spot
        </button>
        <button
          className={activeTab === 'data' ? 'active' : ''}
          onClick={() => setActiveTab('data')}
        >
          📊 Popular Spots
        </button>
      </nav>

      <main className="app-main">
        {activeTab === 'map' && <CampusMap surveyData={surveyData} />}
        {activeTab === 'survey' && <SurveyForm onSubmit={handleSurveySubmit} />}
        {activeTab === 'data' && <DataVisualization surveyData={surveyData} />}
      </main>
    </div>
  )
}

export default App
