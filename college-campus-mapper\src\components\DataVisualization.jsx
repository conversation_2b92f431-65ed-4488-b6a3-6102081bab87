import { useState, useMemo } from 'react'
import './DataVisualization.css'

const DataVisualization = ({ surveyData }) => {
  const [activeChart, setActiveChart] = useState('locations')

  // Process data for different visualizations
  const processedData = useMemo(() => {
    if (!surveyData || surveyData.length === 0) {
      return {
        locations: [],
        timeOfDay: [],
        activities: [],
        departments: [],
        groupSizes: [],
        ratings: []
      }
    }

    // Location popularity
    const locationCounts = {}
    const timeCounts = {}
    const activityCounts = {}
    const departmentCounts = {}
    const groupSizeCounts = {}
    const ratingCounts = {}

    surveyData.forEach(data => {
      // Locations
      locationCounts[data.location] = (locationCounts[data.location] || 0) + 1
      
      // Time of day
      timeCounts[data.timeOfDay] = (timeCounts[data.timeOfDay] || 0) + 1
      
      // Activities
      data.activities.forEach(activity => {
        activityCounts[activity] = (activityCounts[activity] || 0) + 1
      })
      
      // Departments
      departmentCounts[data.department] = (departmentCounts[data.department] || 0) + 1
      
      // Group sizes
      groupSizeCounts[data.groupSize] = (groupSizeCounts[data.groupSize] || 0) + 1
      
      // Ratings
      const rating = Math.floor(data.rating)
      ratingCounts[rating] = (ratingCounts[rating] || 0) + 1
    })

    const locationNames = {
      'library': 'Library',
      'cafeteria': 'Cafeteria',
      'garden': 'Campus Garden',
      'auditorium': 'Auditorium Lobby',
      'courtyard': 'Central Courtyard',
      'computer-lab': 'Computer Lab'
    }

    return {
      locations: Object.entries(locationCounts)
        .map(([key, value]) => ({ name: locationNames[key] || key, count: value }))
        .sort((a, b) => b.count - a.count),
      timeOfDay: Object.entries(timeCounts)
        .map(([key, value]) => ({ name: key, count: value }))
        .sort((a, b) => b.count - a.count),
      activities: Object.entries(activityCounts)
        .map(([key, value]) => ({ name: key, count: value }))
        .sort((a, b) => b.count - a.count),
      departments: Object.entries(departmentCounts)
        .map(([key, value]) => ({ name: key, count: value }))
        .sort((a, b) => b.count - a.count),
      groupSizes: Object.entries(groupSizeCounts)
        .map(([key, value]) => ({ name: key, count: value }))
        .sort((a, b) => b.count - a.count),
      ratings: Object.entries(ratingCounts)
        .map(([key, value]) => ({ name: `${key}/10`, count: value }))
        .sort((a, b) => parseInt(a.name) - parseInt(b.name))
    }
  }, [surveyData])

  const BarChart = ({ data, title, color = '#3498db' }) => {
    if (!data || data.length === 0) {
      return (
        <div className="chart-container">
          <h3>{title}</h3>
          <div className="no-data">No data available yet. Submit some surveys to see results!</div>
        </div>
      )
    }

    const maxCount = Math.max(...data.map(item => item.count))

    return (
      <div className="chart-container">
        <h3>{title}</h3>
        <div className="bar-chart">
          {data.map((item, index) => (
            <div key={index} className="bar-item">
              <div className="bar-label">{item.name}</div>
              <div className="bar-container">
                <div 
                  className="bar"
                  style={{ 
                    width: `${(item.count / maxCount) * 100}%`,
                    backgroundColor: color
                  }}
                >
                  <span className="bar-value">{item.count}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  const StatCard = ({ title, value, icon, color }) => (
    <div className="stat-card" style={{ borderLeftColor: color }}>
      <div className="stat-icon">{icon}</div>
      <div className="stat-content">
        <div className="stat-value">{value}</div>
        <div className="stat-title">{title}</div>
      </div>
    </div>
  )

  const totalResponses = surveyData.length
  const uniqueLocations = new Set(surveyData.map(d => d.location)).size
  const averageRating = surveyData.length > 0 
    ? (surveyData.reduce((sum, d) => sum + d.rating, 0) / surveyData.length).toFixed(1)
    : 0
  const mostPopularLocation = processedData.locations[0]?.name || 'N/A'

  return (
    <div className="data-visualization">
      <div className="viz-header">
        <h2>📊 Campus Insights</h2>
        <p>Discover trends and popular spots based on student feedback</p>
      </div>

      {/* Summary Statistics */}
      <div className="stats-grid">
        <StatCard 
          title="Total Responses" 
          value={totalResponses} 
          icon="📝" 
          color="#3498db"
        />
        <StatCard 
          title="Locations Covered" 
          value={uniqueLocations} 
          icon="📍" 
          color="#e74c3c"
        />
        <StatCard 
          title="Average Rating" 
          value={`${averageRating}/10`} 
          icon="⭐" 
          color="#f39c12"
        />
        <StatCard 
          title="Most Popular Spot" 
          value={mostPopularLocation} 
          icon="🏆" 
          color="#27ae60"
        />
      </div>

      {/* Chart Navigation */}
      <div className="chart-nav">
        <button 
          className={activeChart === 'locations' ? 'active' : ''}
          onClick={() => setActiveChart('locations')}
        >
          📍 Popular Locations
        </button>
        <button 
          className={activeChart === 'time' ? 'active' : ''}
          onClick={() => setActiveChart('time')}
        >
          🕐 Best Times
        </button>
        <button 
          className={activeChart === 'activities' ? 'active' : ''}
          onClick={() => setActiveChart('activities')}
        >
          🎯 Top Activities
        </button>
        <button 
          className={activeChart === 'departments' ? 'active' : ''}
          onClick={() => setActiveChart('departments')}
        >
          🎓 By Department
        </button>
        <button 
          className={activeChart === 'groups' ? 'active' : ''}
          onClick={() => setActiveChart('groups')}
        >
          👥 Group Sizes
        </button>
        <button 
          className={activeChart === 'ratings' ? 'active' : ''}
          onClick={() => setActiveChart('ratings')}
        >
          ⭐ Ratings
        </button>
      </div>

      {/* Charts */}
      <div className="charts-section">
        {activeChart === 'locations' && (
          <BarChart 
            data={processedData.locations} 
            title="Most Popular Campus Locations" 
            color="#3498db"
          />
        )}
        {activeChart === 'time' && (
          <BarChart 
            data={processedData.timeOfDay} 
            title="Best Times to Visit" 
            color="#e74c3c"
          />
        )}
        {activeChart === 'activities' && (
          <BarChart 
            data={processedData.activities} 
            title="Most Common Activities" 
            color="#f39c12"
          />
        )}
        {activeChart === 'departments' && (
          <BarChart 
            data={processedData.departments} 
            title="Responses by Department" 
            color="#9b59b6"
          />
        )}
        {activeChart === 'groups' && (
          <BarChart 
            data={processedData.groupSizes} 
            title="Preferred Group Sizes" 
            color="#27ae60"
          />
        )}
        {activeChart === 'ratings' && (
          <BarChart 
            data={processedData.ratings} 
            title="Rating Distribution" 
            color="#e67e22"
          />
        )}
      </div>

      {/* Recent Feedback */}
      {surveyData.length > 0 && (
        <div className="recent-feedback">
          <h3>Recent Student Feedback</h3>
          <div className="feedback-list">
            {surveyData.slice(-5).reverse().map((data, index) => (
              <div key={index} className="feedback-item">
                <div className="feedback-header">
                  <strong>{data.name || 'Anonymous'}</strong>
                  <span className="feedback-location">
                    {processedData.locations.find(l => l.name.toLowerCase().includes(data.location))?.name || data.location}
                  </span>
                  <span className="feedback-rating">⭐ {data.rating}/10</span>
                </div>
                <div className="feedback-reason">"{data.reason}"</div>
                <div className="feedback-meta">
                  {data.department} • {data.timeOfDay} • {data.groupSize}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default DataVisualization
